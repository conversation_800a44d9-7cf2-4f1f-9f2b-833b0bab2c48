import { createRouter, createWebHistory } from 'vue-router'
import BudgetReportView from '../views/BudgetReportView.vue'
import SalaryTaxView from '../views/SalaryTaxView.vue'
import QuickQueryView from '../views/QuickQueryView.vue'
import OtherFeaturesView from '../views/OtherFeaturesView.vue'
import DataAnalysisView from '../views/DataAnalysisView.vue'
import CapitalFlowView from '../views/CapitalFlowView.vue'
import OneClickReportView from '../views/OneClickReportView.vue'
import SamrtWorkerView from '../views/SamrtWorkerView.vue'
import SmartReconciliationView from '../views/SmartReconciliationView.vue'
import SettingsView from '../views/SettingsView.vue'
import TableComponentDemo from '../views/TableComponentDemo.vue'
import ProjectReportView from '../views/ProjectReportView.vue'
import VTableTestView from '../views/VTableTestView.vue'
import VTableScrollTest from '../views/VTableScrollTest.vue'
import EnhancedTableDemo from '../views/EnhancedTableDemo.vue'


const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      redirect: '/data-analysis'
    },
    {
      path: '/data-analysis',
      name: 'data-analysis',
      component: DataAnalysisView,
      meta: {
        title: '我的账面',
        icon: 'fa-database'
      }
    },
    {
      path: '/budget-report',
      name: 'budget-report',
      component: BudgetReportView,
      meta: {
        title: '报表快算',
        icon: 'fa-chart-bar'
      }
    },
    {
      path: '/salary-tax',
      name: 'salary-tax',
      component: SalaryTaxView,
      meta: {
        title: '薪酬个税',
        icon: 'fa-money-bill'
      }
    },
    {
      path: '/capital-flow',
      name: 'capital-flow',
      component: CapitalFlowView,
      meta: {
        title: '资金流动',
        icon: 'fa-exchange-alt'
      }
    },
    {
      path: '/one-click-report',
      name: 'one-click-report',
      component: OneClickReportView,
      meta: {
        title: '一键报告',
        icon: 'fa-file-alt'
      }
    },
    {
      path: '/project-report',
      name: 'project-report',
      component: ProjectReportView,
      meta: {
        title: '项目台账',
        icon: 'fa-folder'
      }
    },
    {
      path: '/samrt-worker',
      name: 'samrt-worker',
      component: SamrtWorkerView,
      meta: {
        title: '工人专区',
        icon: 'fa-calculator'
      }
    },
    {
      path: '/smart-reconciliation',
      name: 'smart-reconciliation',
      component: SmartReconciliationView,
      meta: {
        title: '对账抵消',
        icon: 'fa-folder'
      }
    },
    {
      path: '/quick-query',
      name: 'quick-query',
      component: QuickQueryView,
      meta: {
        title: '快速查询',
        icon: 'fa-search'
      }
    },
    {
      path: '/settings',
      name: 'settings',
      component: SettingsView,
      meta: {
        title: '系统设置',
        icon: 'fa-cogs'
      }
    },
    {
      path: '/table-demo',
      name: 'table-demo',
      component: TableComponentDemo,
      meta: {
        title: '表格组件演示',
        icon: 'fa-table'
      }
    },
    {
      path: '/vtable-test',
      name: 'vtable-test',
      component: VTableTestView,
      meta: {
        title: 'VTable测试',
        icon: 'fa-table'
      }
    },
    {
      path: '/vtable-scroll-test',
      name: 'vtable-scroll-test',
      component: VTableScrollTest,
      meta: {
        title: 'VTable滚动测试',
        icon: 'fa-arrows-h'
      }
    },
    {
      path: '/enhanced-table-demo',
      name: 'enhanced-table-demo',
      component: EnhancedTableDemo,
      meta: {
        title: '增强表格演示',
        icon: 'fa-table'
      }
    }
  ],
})

export default router
