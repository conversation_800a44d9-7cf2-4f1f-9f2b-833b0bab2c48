<script setup>
import { ref, onMounted } from 'vue';
import * as echarts from 'echarts';
import axios from 'axios';

// API service for data fetching
const apiService = {
  async fetchAnalysisData() {
    try {
      const response = await axios.post('http://127.0.0.1:8000/api/financial-analysis/data');
      return response.data;
    } catch (error) {
      console.error('Error fetching financial analysis data:', error);
      return null;
    }
  }
};

// Reactive data for charts
const incomeData = ref({
  categories: ['总收入', '已确认收入', '未确认收入'],
  values: [],
  monthlyTrend: [[], [], []],
  months: []
});

const profitData = ref({
  categories: ['总利润', '营业利润', '净利润'],
  values: [],
  monthlyTrend: [[], [], []],
  months: []
});

const fundsData = ref({
  categories: ['总资金', '短期借款', '应付票据', '应付保理', '净资金'],
  values: [],
  monthlyTrend: [[], [], [], [], []], // 初始化为5个空数组，对应5个类别
  months: []
});

const assetsData = ref({
  categories: ['总资产', '流动资产', '非流动资产', '净资产'],
  values: [],
  monthlyTrend: [[], [], [], []],
  months: []
});

const liabilitiesData = ref({
  categories: [],
  values: [],
  monthlyTrend: [],
  months: []
});

const taxData = ref({
  categories: ['未开票收入', '未开票金额', '未收票金额'],
  values: [],
  monthlyTrend: [[], [], []],
  months: []
});  // Stats for the dashboard header
const dashboardStats = ref({
  totalIncome: '¥ 0万',
  totalProfit: '¥ 0万',
  totalFunds: '¥ 0万',
  totalAssets: '¥ 0万',
  totalLiabilities: '¥ 0万',
  uninvoicedIncome: '¥ 0万'
});

// Loading state
const isLoading = ref(true);

// Fetch data and initialize charts
async function fetchDataAndInitCharts() {
  try {
    isLoading.value = true;
    
    // Fetch data from API
    const data = await apiService.fetchAnalysisData();
    
    if (data) {
      // Update all data refs with API response
      incomeData.value = data.incomeData || incomeData.value;
      profitData.value = data.profitData || profitData.value;
      fundsData.value = data.fundsData || fundsData.value;
      assetsData.value = data.assetsData || assetsData.value;
      liabilitiesData.value = data.liabilitiesData || liabilitiesData.value;
      taxData.value = data.taxData || taxData.value;
      
      // Update dashboard stats (ensuring first row data is displayed)
      dashboardStats.value = {
        totalIncome: data.dashboardStats?.totalIncome || '¥ 14,060.0万',
        totalProfit: data.dashboardStats?.totalProfit || '¥ 2,900.0万',
        totalFunds: data.dashboardStats?.totalFunds || '¥ 12,000.0万',
        totalAssets: data.dashboardStats?.totalAssets || '¥ 15,700.0万',
        totalLiabilities: data.dashboardStats?.totalLiabilities || '¥ 15,000.0万',
        uninvoicedIncome: data.dashboardStats?.uninvoicedIncome || '¥ 5,800.0万'
      };
    } else {
      // If API fails, use default mock data for demo purposes
      setDefaultMockData();
    }
  } catch (error) {
    console.error('Error in fetchDataAndInitCharts:', error);
    setDefaultMockData();
  } finally {
    isLoading.value = false;
    // Initialize charts after data is loaded
    initCharts();
  }
}

// Set default mock data if API fails
function setDefaultMockData() {
  // 收入数据
  incomeData.value = {
    categories: ['总收入', '已确认收入', '未确认收入'],
    values: [14060, 11200, 2860],
    monthlyTrend: [
      [1100, 1150, 1200, 1180, 1220, 1250, 1280, 1300, 1320, 1350, 1380, 1406], // 总收入
      [900, 930, 960, 950, 980, 1000, 1020, 1040, 1060, 1080, 1100, 1120],      // 已确认收入
      [200, 220, 240, 230, 240, 250, 260, 260, 260, 270, 280, 286]              // 未确认收入
    ],
    months: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
  };

  // 利润数据
  profitData.value = {
    categories: ['总利润', '营业利润', '净利润'],
    values: [2900, 2300, 2100],
    monthlyTrend: [
      [220, 230, 240, 235, 245, 250, 255, 260, 265, 270, 280, 290],  // 总利润
      [180, 185, 195, 190, 200, 205, 210, 215, 220, 225, 230, 230],  // 营业利润
      [165, 170, 175, 173, 180, 185, 190, 195, 200, 205, 208, 210]   // 净利润
    ],
    months: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
  };

  // 资金数据
  fundsData.value = {
    categories: ['总资金', '短期借款', '应付票据', '应付保理', '净资金'],
    values: [12000, 3000, 2500, 1500, 5000],
    monthlyTrend: [
      [11000, 11200, 11500, 11800, 12000, 12200, 12300, 12100, 12000, 11800, 11900, 12000], // 总资金
      [2800, 2900, 3000, 3100, 3000, 2900, 2800, 2700, 2800, 2900, 3000, 3000],             // 短期借款
      [2200, 2300, 2400, 2500, 2500, 2600, 2700, 2600, 2500, 2400, 2500, 2500],             // 应付票据
      [1300, 1400, 1500, 1600, 1500, 1400, 1300, 1400, 1500, 1600, 1500, 1500],             // 应付保理
      [4700, 4600, 4600, 4600, 5000, 5300, 5500, 5400, 5200, 4900, 4900, 5000]              // 净资金
    ],
    months: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
  };

  // 资产数据
  assetsData.value = {
    categories: ['总资产', '流动资产', '非流动资产', '净资产'],
    values: [25000, 15000, 10000, 12000],
    monthlyTrend: [
      [23000, 23200, 23500, 23800, 24000, 24200, 24300, 24100, 24000, 23800, 23900, 24000], // 总资产
      [14000, 14200, 14500, 14800, 15000, 15200, 15300, 15100, 15000, 14800, 14900, 15000],  // 流动资产
      [9000, 9000, 9000, 9000, 9000, 9000, 9000, 9000, 9000, 9000, 9000, 9000],             // 非流动资产
      [11500, 11600, 11700, 11800, 12000, 12200, 12300, 12200, 12100, 12000, 12050, 12000]   // 净资产
    ],
    months: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
  };

  // 负债数据
  liabilitiesData.value = {
    categories: ['总负债', '流动负债', '非流动负债', '资产负债率(%)'],
    values: [13000, 10000, 3000, 52],
    monthlyTrend: [
      [12500, 12600, 12700, 12800, 13000, 13200, 13300, 13200, 13100, 13000, 13050, 13000], // 总负债
      [9500, 9600, 9700, 9800, 10000, 10200, 10300, 10200, 10100, 10000, 10050, 10000],      // 流动负债
      [3000, 3000, 3000, 3000, 3000, 3000, 3000, 3000, 3000, 3000, 3000, 3000],             // 非流动负债
      [54.3, 54.3, 54.0, 53.8, 54.2, 54.5, 54.7, 54.8, 54.6, 54.6, 54.6, 54.2]              // 资产负债率(%)
    ],
    months: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
  };

  // 税务数据
  taxData.value = {
    categories: ['未开票收入', '未开票金额', '未收票金额'],
    values: [5800, 4200, 3500],
    monthlyTrend: [
      [5500, 5600, 5700, 5800, 5900, 6000, 5900, 5800, 5700, 5800, 5900, 5800], // 未开票收入
      [4000, 4100, 4200, 4300, 4200, 4100, 4000, 4100, 4200, 4300, 4200, 4200],  // 未开票金额
      [3300, 3400, 3500, 3600, 3500, 3400, 3300, 3400, 3500, 3600, 3500, 3500]   // 未收票金额
    ],
    months: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
  };

  // 仪表盘统计数据
  dashboardStats.value = {
    totalIncome: `¥ ${(incomeData.value.values[0]).toLocaleString()}万`,
    totalProfit: `¥ ${(profitData.value.values[0]).toLocaleString()}万`,
    totalFunds: `¥ ${(fundsData.value.values[0]).toLocaleString()}万`,
    totalAssets: `¥ ${(assetsData.value.values[0]).toLocaleString()}万`,
    totalLiabilities: `¥ ${(liabilitiesData.value.values[0]).toLocaleString()}万`,
    uninvoicedIncome: `¥ ${(taxData.value.values[0]).toLocaleString()}万`
  };
}

// Initialize all charts
function initCharts() {
  initIncomeChart();
  initProfitChart();
  initFundsChart();
  initAssetsChart();
  initLiabilitiesChart();
  initTaxChart();
}

// Initialize charts after component mount
onMounted(() => {
  // Fetch data and initialize charts when component is mounted
  fetchDataAndInitCharts();
});

function initIncomeChart() {
  const chartDom = document.getElementById('incomeChart');
  if (!chartDom) return; // Safety check
  
  const myChart = echarts.init(chartDom);
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: incomeData.value.categories,
      bottom: '0%',
      textStyle: {
        color: '#333'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      top: '5%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        data: incomeData.value.months,
        axisLabel: {
          interval: 1,
          rotate: 30
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '金额（万元）'
      }
    ],
    series: [
      {
        name: incomeData.value.categories[0], // 总收入
        type: 'line',
        emphasis: {
          focus: 'series'
        },
        symbol: 'circle',
        symbolSize: 8,
        itemStyle: {
          color: '#fbbc05'
        },
        lineStyle: {
          width: 3
        },
        data: incomeData.value.monthlyTrend[0]
      },
      {
        name: incomeData.value.categories[1], // 已确认收入
        type: 'line',
        emphasis: {
          focus: 'series'
        },
        symbol: 'circle',
        symbolSize: 6,
        itemStyle: {
          color: '#4285f4'
        },
        data: incomeData.value.monthlyTrend[1]
      },
      {
        name: incomeData.value.categories[2], // 未确认收入
        type: 'line',
        emphasis: {
          focus: 'series'
        },
        symbol: 'circle',
        symbolSize: 6,
        itemStyle: {
          color: '#ea4335'
        },
        data: incomeData.value.monthlyTrend[2]
      }
    ]
  };
  
  myChart.setOption(option);
  window.addEventListener('resize', () => myChart.resize());
}

function initProfitChart() {
  const chartDom = document.getElementById('profitChart');
  if (!chartDom) return; // Safety check
  
  const myChart = echarts.init(chartDom);
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: profitData.value.categories,
      bottom: '0%',
      textStyle: {
        color: '#333'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      top: '5%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        data: profitData.value.months,
        axisLabel: {
          interval: 1,
          rotate: 30
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '金额（万元）'
      }
    ],
    series: [
      {
        name: profitData.value.categories[0], // 总利润
        type: 'line',
        emphasis: {
          focus: 'series'
        },
        symbol: 'circle',
        symbolSize: 8,
        itemStyle: {
          color: '#fbbc05'
        },
        lineStyle: {
          width: 3
        },
        data: profitData.value.monthlyTrend[0]
      },
      {
        name: profitData.value.categories[1], // 营业利润
        type: 'line',
        emphasis: {
          focus: 'series'
        },
        symbol: 'circle',
        symbolSize: 6,
        itemStyle: {
          color: '#4285f4'
        },
        data: profitData.value.monthlyTrend[1]
      },
      {
        name: profitData.value.categories[2], // 净利润
        type: 'line',
        emphasis: {
          focus: 'series'
        },
        symbol: 'circle',
        symbolSize: 6,
        itemStyle: {
          color: '#34a853'
        },
        data: profitData.value.monthlyTrend[2]
      }
    ]
  };
  
  myChart.setOption(option);
  window.addEventListener('resize', () => myChart.resize());
}

function initFundsChart() {
  const chartDom = document.getElementById('fundsChart');
  if (!chartDom) return; // Safety check
  
  const myChart = echarts.init(chartDom);
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function(params) {
        let result = params[0].axisValue + '<br/>';
        params.forEach((item) => {
          result += item.marker + item.seriesName + ': ¥' + item.value + '万<br/>';
        });
        return result;
      }
    },
    legend: {
      data: fundsData.value.categories,
      bottom: '0%',
      textStyle: {
        color: '#333'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      top: '5%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        data: fundsData.value.months,
        axisLabel: {
          interval: 1,
          rotate: 30
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '金额（万元）',
        axisLabel: {
          formatter: '¥{value}万'
        }
      }
    ],
    series: fundsData.value.categories.map((category, index) => ({
      name: category,
      type: 'line',
      emphasis: {
        focus: 'series'
      },
      symbol: 'circle',
      symbolSize: index === 0 || index === 4 ? 8 : 6,
      itemStyle: {
        color: index === 0 ? '#fbbc05' : 
              index === 1 ? '#4285f4' : 
              index === 2 ? '#ea4335' :
              index === 3 ? '#34a853' :
                           '#800080'
      },
      lineStyle: {
        width: index === 0 || index === 4 ? 3 : 1,
        type: index === 4 ? 'dashed' : 'solid'
      },
      data: fundsData.value.monthlyTrend[index]
    }))
  };
  
  myChart.setOption(option);
  window.addEventListener('resize', () => myChart.resize());
}

function initAssetsChart() {
  const chartDom = document.getElementById('assetsChart');
  if (!chartDom) return; // Safety check
  
  const myChart = echarts.init(chartDom);
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: assetsData.value.categories,
      bottom: '0%',
      textStyle: {
        color: '#333'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      top: '5%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        data: assetsData.value.months,
        axisLabel: {
          interval: 1,
          rotate: 30
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '金额（万元）'
      }
    ],
    series: [
      {
        name: assetsData.value.categories[0], // 总资产
        type: 'line',
        emphasis: {
          focus: 'series'
        },
        symbol: 'circle',
        symbolSize: 8,
        itemStyle: {
          color: '#fbbc05'
        },
        lineStyle: {
          width: 3
        },
        data: assetsData.value.monthlyTrend[0]
      },
      {
        name: assetsData.value.categories[1], // 流动资产
        type: 'line',
        emphasis: {
          focus: 'series'
        },
        symbol: 'circle',
        symbolSize: 6,
        itemStyle: {
          color: '#4285f4'
        },
        data: assetsData.value.monthlyTrend[1]
      },
      {
        name: assetsData.value.categories[2], // 非流动资产
        type: 'line',
        emphasis: {
          focus: 'series'
        },
        symbol: 'circle',
        symbolSize: 6,
        itemStyle: {
          color: '#ea4335'
        },
        data: assetsData.value.monthlyTrend[2]
      },
      {
        name: assetsData.value.categories[3], // 净资产
        type: 'line',
        emphasis: {
          focus: 'series'
        },
        symbol: 'circle',
        symbolSize: 8,
        itemStyle: {
          color: '#34a853'
        },
        lineStyle: {
          width: 3,
          type: 'dashed'
        },
        data: assetsData.value.monthlyTrend[3]
      }
    ]
  };
  
  myChart.setOption(option);
  window.addEventListener('resize', () => myChart.resize());
}

function initLiabilitiesChart() {
  const chartDom = document.getElementById('liabilitiesChart');
  if (!chartDom) return; // Safety check
  
  const myChart = echarts.init(chartDom);
  
  // 过滤掉资产负债率，只显示实际的负债类型
  const liabilityCategories = liabilitiesData.value.categories.slice(0, 3);
  const liabilityValues = liabilitiesData.value.values.slice(0, 3);
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: function(params) {
        const value = params.value;
        const percent = ((value / liabilityValues.reduce((a, b) => a + b, 0)) * 100).toFixed(1);
        return `${params.seriesName} <br/>${params.name}: ¥${value}万 <br/>(${percent}%)`;
      }
    },
    legend: {
      orient: 'horizontal',
      bottom: '0%',
      data: liabilityCategories,
      textStyle: {
        color: '#333'
      }
    },
    series: [
      {
        name: '负债构成',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: true,
          position: 'outside',
          formatter: '{b}: {d}%'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '16',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: true
        },
        data: liabilityCategories.map((category, index) => {
          return {
            value: liabilityValues[index],
            name: category
          };
        })
      }
    ]
  };
  
  myChart.setOption(option);
  window.addEventListener('resize', () => myChart.resize());
}

function initTaxChart() {
  const chartDom = document.getElementById('taxChart');
  if (!chartDom) return; // Safety check
  
  const myChart = echarts.init(chartDom);
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985'
        }
      },
      formatter: function(params) {
        let result = params[0].axisValue + '<br/>';
        params.forEach((item) => {
          result += item.marker + item.seriesName + ': ¥' + item.value + '万<br/>';
        });
        return result;
      }
    },
    legend: {
      data: taxData.value.categories,
      bottom: '0%',
      textStyle: {
        color: '#333'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      top: '5%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        boundaryGap: false,
        data: taxData.value.months,
        axisLabel: {
          interval: 1,
          rotate: 30
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '金额（万元）',
        axisLabel: {
          formatter: '¥{value}万'
        }
      }
    ],
    series: taxData.value.categories.map((category, index) => ({
      name: category,
      type: 'line',
      stack: 'Total',
      areaStyle: {
        opacity: 0.3,
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: `rgba(${index * 50}, 128, 128, 0.8)` },
          { offset: 1, color: `rgba(${index * 50}, 128, 128, 0.1)` }
        ])
      },
      emphasis: {
        focus: 'series'
      },
      symbol: 'circle',
      symbolSize: 8,
      itemStyle: {
        color: `rgb(${index * 50}, 128, 128)`
      },
      data: taxData.value.monthlyTrend[index]
    }))
  };
  
  myChart.setOption(option);
  window.addEventListener('resize', () => myChart.resize());
}
</script>

<template>
  <div class="data-analysis-container">
    <div class="dashboard-header">
      <h1>财务数据分析总览</h1>
      <div class="dashboard-stats">
        <div class="stat-card income">
          <div class="stat-value">{{ dashboardStats.totalIncome }}</div>
          <div class="stat-label">总收入</div>
          <div class="stat-change positive">↑ 8.2%</div>
        </div>
        <div class="stat-card profit">
          <div class="stat-value">{{ dashboardStats.totalProfit }}</div>
          <div class="stat-label">总利润</div>
          <div class="stat-change positive">↑ 5.4%</div>
        </div>
        <div class="stat-card funds">
          <div class="stat-value">{{ dashboardStats.totalFunds }}</div>
          <div class="stat-label">总资金</div>
          <div class="stat-change positive">↑ 3.1%</div>
        </div>
        <div class="stat-card assets">
          <div class="stat-value">{{ dashboardStats.totalAssets }}</div>
          <div class="stat-label">总资产</div>
          <div class="stat-change positive">↑ 2.5%</div>
        </div>
        <div class="stat-card liabilities">
          <div class="stat-value">{{ dashboardStats.totalLiabilities }}</div>
          <div class="stat-label">总负债</div>
          <div class="stat-change negative">↑ 1.8%</div>
        </div>
        <div class="stat-card tax">
          <div class="stat-value">{{ dashboardStats.uninvoicedIncome }}</div>
          <div class="stat-label">未开票收入</div>
          <div class="stat-change negative">↑ 4.1%</div>
        </div>
      </div>
    </div>

    <div class="charts-grid">
      <!-- 收入区域 -->
      <div class="chart-container income-section">
        <h2 class="section-title">收入分析</h2>
        <div class="section-description">总收入与未结转收入分析</div>
        <div id="incomeChart" class="chart"></div>
      </div>
      
      <!-- 利润区域 -->
      <div class="chart-container profit-section">
        <h2 class="section-title">利润分析</h2>
        <div class="section-description">利润与利润率分析</div>
        <div id="profitChart" class="chart"></div>
      </div>
      
      <!-- 资金区域 -->
      <div class="chart-container funds-section">
        <h2 class="section-title">资金分析</h2>
        <div class="section-description">总资金、短期借款、应付票据、应付保理、净资金</div>
        <div id="fundsChart" class="chart"></div>
      </div>
      
      <!-- 资产区域 -->
      <div class="chart-container assets-section">
        <h2 class="section-title">资产分析</h2>
        <div class="section-description">合同资产、应收账款</div>
        <div id="assetsChart" class="chart"></div>
      </div>
      
      <!-- 负债区域 -->
      <div class="chart-container liabilities-section">
        <h2 class="section-title">负债分析</h2>
        <div class="section-description">应付分包、应付材料、应付劳务、应付保理、应付票据</div>
        <div id="liabilitiesChart" class="chart"></div>
      </div>
      
      <!-- 税务区域 -->
      <div class="chart-container tax-section">
        <h2 class="section-title">税务分析</h2>
        <div class="section-description">未开票收入、未开票金额、未收票金额</div>
        <div id="taxChart" class="chart"></div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.data-analysis-container {
  width: 100%;
  height: 100%;
  padding: 0;
  overflow-y: auto;
  background-color: #f8f9fa;
  display: flex;
  flex-direction: column;
}

.dashboard-header {
  background: linear-gradient(135deg, #1967d2, #4285f4);
  color: white;
  padding: 24px;
  border-radius: 12px;
  margin: 24px 24px 0 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
}

.dashboard-header h1 {
  margin: 0 0 16px 0;
  font-weight: 500;
  font-size: 1.8rem;
  text-align: center;
}

.dashboard-stats {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 16px;
}

.stat-card {
  background-color: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  padding: 16px;
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

/* Specific card styling */
.stat-card.income {
  background: linear-gradient(135deg, rgba(66, 133, 244, 0.1), rgba(66, 133, 244, 0.2));
  border-bottom: 3px solid #4285f4;
}

.stat-card.profit {
  background: linear-gradient(135deg, rgba(52, 168, 83, 0.1), rgba(52, 168, 83, 0.2));
  border-bottom: 3px solid #34a853;
}

.stat-card.funds {
  background: linear-gradient(135deg, rgba(251, 188, 5, 0.1), rgba(251, 188, 5, 0.2));
  border-bottom: 3px solid #fbbc05;
}

.stat-card.assets {
  background: linear-gradient(135deg, rgba(128, 0, 128, 0.1), rgba(128, 0, 128, 0.2));
  border-bottom: 3px solid #800080;
}

.stat-card.liabilities {
  background: linear-gradient(135deg, rgba(234, 67, 53, 0.1), rgba(234, 67, 53, 0.2));
  border-bottom: 3px solid #ea4335;
}

.stat-card.tax {
  background: linear-gradient(135deg, rgba(0, 128, 128, 0.1), rgba(0, 128, 128, 0.2));
  border-bottom: 3px solid #008080;
}

.stat-value {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 0.85rem;
  opacity: 0.9;
  margin-bottom: 4px;
}

.stat-change {
  font-size: 0.8rem;
  font-weight: 500;
}

.stat-change.positive {
  color: #a7f3d0;
}

.stat-change.negative {
  color: #fecaca;
}

.charts-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24px;
  padding: 24px;
  flex: 1;
  min-height: 0;
}

.chart-container {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  padding: 20px;
  display: flex;
  flex-direction: column;
  min-height: 350px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
}

.chart-container:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* Specific chart section styling */
.income-section {
  border-top: 5px solid #4285f4;
}

.profit-section {
  border-top: 5px solid #34a853;
}

.funds-section {
  border-top: 5px solid #fbbc05;
}

.assets-section {
  border-top: 5px solid #800080;
}

.liabilities-section {
  border-top: 5px solid #ea4335;
}

.tax-section {
  border-top: 5px solid #008080;
}

.section-title {
  font-size: 1.3rem;
  font-weight: 500;
  margin: 0 0 5px 0;
  color: #333;
}

.section-description {
  font-size: 0.85rem;
  color: #666;
  margin-bottom: 15px;
}

.chart {
  width: 100%;
  height: 100%;
  min-height: 280px;
  flex-grow: 1;
}

@media (max-width: 1200px) {
  .dashboard-stats {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 768px) {
  .dashboard-stats {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .charts-grid {
    grid-template-columns: 1fr;
    padding: 16px;
  }
  
  .dashboard-header {
    margin: 16px 16px 0 16px;
  }
}

@media (max-width: 480px) {
  .dashboard-stats {
    grid-template-columns: 1fr;
  }
}
</style>
