<template>
  <div class="report-dashboard">
    <!-- 左侧报告切换栏 -->
    <div class="report-sidebar">
      <button 
        v-for="(report, index) in reports" 
        :key="index"
        type="button"
        class="report-tab"
        :class="{ 'active': activeReport === report.id }"
        @click="activeReport = report.id"
      >
        <i :class="report.icon" class="tab-icon"></i>
        <span class="tab-title">{{ report.title }}</span>
      </button>
    </div>

    <!-- 右侧内容区 -->
    <div class="report-content">
      <div class="content-wrapper">
        <!-- 指标情况简报 -->
        <div v-if="activeReport === 'metrics'" class="report-section">
          <h2>指标情况简报</h2>
          <div class="report-date">{{ currentDate }}</div>
          
          <div class="echart-container">
            <div ref="metricsChart" style="width: 100%; height: 400px;"></div>
          </div>
          
          <div class="markdown-content">
            <h3>关键指标概览</h3>
            <table class="report-table">
              <thead>
                <tr>
                  <th>指标名称</th>
                  <th>当前值</th>
                  <th>环比</th>
                  <th>同比</th>
                  <th>目标值</th>
                  <th>完成率</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(item, index) in metricsData" :key="index">
                  <td>{{ item.name }}</td>
                  <td>{{ item.current }}</td>
                  <td :class="{ 'text-success': item.mom >= 0, 'text-danger': item.mom < 0 }">
                    {{ item.mom }}%
                  </td>
                  <td :class="{ 'text-success': item.yoy >= 0, 'text-danger': item.yoy < 0 }">
                    {{ item.yoy }}%
                  </td>
                  <td>{{ item.target }}</td>
                  <td>{{ item.completion }}%</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- 稽核情况简报 -->
        <div v-else-if="activeReport === 'audit'" class="report-section">
          <h2>稽核情况简报</h2>
          <div class="report-date">{{ currentDate }}</div>
          
          <div class="echart-container">
            <div ref="auditChart" style="width: 100%; height: 400px;"></div>
          </div>
          
          <div class="markdown-content">
            <h3>稽核问题汇总</h3>
            <table class="report-table">
              <thead>
                <tr>
                  <th>问题类型</th>
                  <th>问题描述</th>
                  <th>责任部门</th>
                  <th>状态</th>
                  <th>发现时间</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(item, index) in auditData" :key="index">
                  <td>{{ item.type }}</td>
                  <td>{{ item.description }}</td>
                  <td>{{ item.department }}</td>
                  <td>
                    <span :class="'status-badge ' + item.statusClass">
                      {{ item.status }}
                    </span>
                  </td>
                  <td>{{ item.date }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- 资金情况简报 -->
        <div v-else-if="activeReport === 'fund'" class="report-section">
          <h2>资金情况简报</h2>
          <div class="report-date">{{ currentDate }}</div>
          
          <div class="echart-container">
            <div ref="fundChart" style="width: 100%; height: 400px;"></div>
          </div>
          
          <div class="markdown-content">
            <h3>资金流水明细</h3>
            <table class="report-table">
              <thead>
                <tr>
                  <th>日期</th>
                  <th>摘要</th>
                  <th>收入</th>
                  <th>支出</th>
                  <th>余额</th>
                  <th>账户</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(item, index) in fundData" :key="index">
                  <td>{{ item.date }}</td>
                  <td>{{ item.desc }}</td>
                  <td class="text-success">{{ item.income || '-' }}</td>
                  <td class="text-danger">{{ item.expense || '-' }}</td>
                  <td>{{ item.balance }}</td>
                  <td>{{ item.account }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts';

export default {
  name: 'OneClickReportView',
  data() {
    return {
      activeReport: 'metrics',
      reports: [
        { id: 'metrics', title: '指标情况简报', icon: 'el-icon-data-line' },
        { id: 'audit', title: '稽核情况简报', icon: 'el-icon-document-checked' },
        { id: 'fund', title: '资金情况简报', icon: 'el-icon-money' },
      ],
      metricsData: [
        { name: '营业收入', current: '2,345.67万', mom: 5.8, yoy: 12.3, target: '2,500.00万', completion: 93.8 },
        { name: '净利润', current: '456.78万', mom: 3.2, yoy: 8.9, target: '500.00万', completion: 91.4 },
        { name: '成本费用率', current: '62.5%', mom: -1.2, yoy: -2.3, target: '65.0%', completion: 96.2 },
        { name: '存货周转天数', current: '45', mom: -2, yoy: -5, target: '50', completion: 90.0 },
      ],
      auditData: [
        { 
          type: '数据异常', 
          description: '2023年12月销售数据异常波动', 
          department: '销售部', 
          status: '处理中', 
          statusClass: 'status-processing',
          date: '2024-01-05' 
        },
        { 
          type: '流程违规', 
          description: '采购审批流程未按规定执行', 
          department: '采购部', 
          status: '待整改', 
          statusClass: 'status-pending',
          date: '2024-01-10' 
        },
        { 
          type: '系统告警', 
          description: '财务系统登录异常', 
          department: 'IT部', 
          status: '已完成', 
          statusClass: 'status-completed',
          date: '2024-01-15' 
        },
      ],
      fundData: [
        { date: '2024-01-20', desc: '销售收入', income: '1,234,567.89', expense: null, balance: '5,432,109.87', account: '中国银行' },
        { date: '2024-01-19', desc: '原材料采购', income: null, expense: '987,654.32', balance: '4,197,541.98', account: '工商银行' },
        { date: '2024-01-18', desc: '工资发放', income: null, expense: '1,234,567.89', balance: '5,185,196.30', account: '建设银行' },
        { date: '2024-01-17', desc: '设备采购', income: null, expense: '2,345,678.90', balance: '6,419,764.19', account: '农业银行' },
      ],
      metricsChart: null,
      auditChart: null,
      fundChart: null,
    };
  },
  computed: {
    currentDate() {
      const now = new Date();
      return `${now.getFullYear()}年${now.getMonth() + 1}月${now.getDate()}日`;
    }
  },
  mounted() {
    // Initialize the chart for the default active tab after the DOM is ready
    this.$nextTick(() => {
      if (this.activeReport === 'metrics' && this.$refs.metricsChart && !this.metricsChart) {
        this.initMetricsChart();
      } 
      // Add similar blocks if your default activeReport could be 'audit' or 'fund'
      // else if (this.activeReport === 'audit' && this.$refs.auditChart && !this.auditChart) {
      //   this.initAuditChart();
      // } else if (this.activeReport === 'fund' && this.$refs.fundChart && !this.fundChart) {
      //   this.initFundChart();
      // }
    });
    window.addEventListener('resize', this.handleResize);
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize);
    if (this.metricsChart) this.metricsChart.dispose();
    if (this.auditChart) this.auditChart.dispose();
    if (this.fundChart) this.fundChart.dispose();
  },
  methods: {
    initMetricsChart() {
      this.metricsChart = echarts.init(this.$refs.metricsChart);
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'shadow' }
        },
        legend: {
          data: ['目标值', '实际值']
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: this.metricsData.map(item => item.name)
        },
        yAxis: {
          type: 'value',
          name: '单位：万元'
        },
        series: [
          {
            name: '目标值',
            type: 'bar',
            data: this.metricsData.map(item => parseFloat(item.target.replace(/[^\d.]/g, '')))
          },
          {
            name: '实际值',
            type: 'bar',
            data: this.metricsData.map(item => parseFloat(item.current.replace(/[^\d.]/g, '')))
          }
        ]
      };
      this.metricsChart.setOption(option);
    },
    initAuditChart() {
      this.auditChart = echarts.init(this.$refs.auditChart);
      const statusCount = this.auditData.reduce((acc, item) => {
        acc[item.status] = (acc[item.status] || 0) + 1;
        return acc;
      }, {});
      
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          right: 10,
          data: Object.keys(statusCount)
        },
        series: [
          {
            name: '状态分布',
            type: 'pie',
            radius: ['50%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '18',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: Object.entries(statusCount).map(([name, value]) => ({
              value,
              name,
              itemStyle: {
                color: this.getStatusColor(name)
              }
            }))
          }
        ]
      };
      this.auditChart.setOption(option);
    },
    initFundChart() {
      this.fundChart = echarts.init(this.$refs.fundChart);
      const dates = [...new Set(this.fundData.map(item => item.date))].reverse();
      const incomeData = dates.map(date => {
        const item = this.fundData.find(d => d.date === date);
        return item ? parseFloat(item.income?.replace(/[^\d.]/g, '') || 0) : 0;
      });
      const expenseData = dates.map(date => {
        const item = this.fundData.find(d => d.date === date);
        return item ? parseFloat(item.expense?.replace(/[^\d.]/g, '') || 0) : 0;
      });
      
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: '#999'
            }
          }
        },
        legend: {
          data: ['收入', '支出']
        },
        xAxis: [
          {
            type: 'category',
            data: dates,
            axisPointer: {
              type: 'shadow'
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '金额',
            axisLabel: {
              formatter: '{value} 元'
            }
          }
        ],
        series: [
          {
            name: '收入',
            type: 'bar',
            data: incomeData,
            itemStyle: {
              color: '#67C23A'
            }
          },
          {
            name: '支出',
            type: 'bar',
            data: expenseData,
            itemStyle: {
              color: '#F56C6C'
            }
          }
        ]
      };
      this.fundChart.setOption(option);
    },
    getStatusColor(status) {
      const colors = {
        '处理中': '#E6A23C',
        '待整改': '#F56C6C',
        '已完成': '#67C23A',
        '已关闭': '#909399'
      };
      return colors[status] || '#909399';
    },
    handleResize() {
      if (this.metricsChart) this.metricsChart.resize();
      if (this.auditChart) this.auditChart.resize();
      if (this.fundChart) this.fundChart.resize();
    }
  },
  watch: {
    activeReport(newReportId) {
      this.$nextTick(() => {
        if (newReportId === 'metrics' && !this.metricsChart && this.$refs.metricsChart) {
          this.initMetricsChart();
        } else if (newReportId === 'audit' && !this.auditChart && this.$refs.auditChart) {
          this.initAuditChart();
        } else if (newReportId === 'fund' && !this.fundChart && this.$refs.fundChart) {
          this.initFundChart();
        }
        // Ensure charts are resized correctly after tab switch / initialization
        this.handleResize();
      });
    }
  }
};
</script>

<style scoped>
.report-dashboard {
  display: flex;
  height: calc(100vh - 84px);
  background-color: #f5f7fa;
  overflow: hidden;
}

.report-sidebar {
  width: 200px;
  background-color: #fff;
  box-shadow: 2px 0 8px 0 rgba(29, 35, 41, 0.05);
  padding: 16px 0;
  overflow-y: auto;
}

.report-tab {
  display: flex;
  align-items: center;
  padding: 12px 24px;
  cursor: pointer;
  transition: all 0.3s;
  color: #606266;
  width: 100%;
  background: none;
  border: none;
  text-align: left;
  outline: none;
}

.report-tab:hover {
  background-color: #f5f7fa;
  color: #409EFF;
}

.report-tab.active {
  background-color: #ecf5ff;
  color: #409EFF;
  border-right: 3px solid #409EFF;
}

.tab-icon {
  margin-right: 8px;
  font-size: 18px;
}

.tab-title {
  font-size: 14px;
}

.report-content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
}

.content-wrapper {
  max-width: 900px;
  margin: 0 auto;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  padding: 32px;
}

.report-section h2 {
  text-align: center;
  margin-bottom: 8px;
  color: #303133;
}

.report-date {
  text-align: center;
  color: #909399;
  margin-bottom: 24px;
  font-size: 14px;
}

.echart-container {
  margin: 24px 0;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 16px;
  background-color: #fff;
}

.markdown-content {
  margin-top: 32px;
}

.markdown-content h3 {
  margin: 24px 0 16px;
  color: #303133;
  font-size: 18px;
  font-weight: 500;
  border-left: 4px solid #409EFF;
  padding-left: 12px;
}

.report-table {
  width: 100%;
  border-collapse: collapse;
  margin: 16px 0;
  font-size: 14px;
}

.report-table th,
.report-table td {
  border: 1px solid #ebeef5;
  padding: 12px 16px;
  text-align: left;
}

.report-table th {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 500;
}

.report-table tbody tr:hover {
  background-color: #f5f7fa;
}

.text-success {
  color: #67C23A;
}

.text-danger {
  color: #F56C6C;
}

.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-processing {
  background-color: #fdf6ec;
  color: #E6A23C;
}

.status-pending {
  background-color: #fef0f0;
  color: #F56C6C;
}

.status-completed {
  background-color: #f0f9eb;
  color: #67C23A;
}

/* 响应式调整 */
@media (max-width: 992px) {
  .report-dashboard {
    flex-direction: column;
    height: auto;
  }
  
  .report-sidebar {
    width: 100%;
    display: flex;
    overflow-x: auto;
    padding: 8px 0;
  }
  
  .report-tab {
    white-space: nowrap;
    padding: 8px 16px;
  }
  
  .report-content {
    padding: 16px;
  }
  
  .content-wrapper {
    padding: 16px;
  }
}
</style>