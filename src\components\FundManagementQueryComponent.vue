<template>
  <BaseQueryComponent
    title="资金整理"
    :query-fields="queryFields"
    :mock-data="mockData"
    @back="$emit('back')"
  />
</template>

<script setup>
import BaseQueryComponent from './BaseQueryComponent.vue'

defineEmits(['back'])

const queryFields = [
  {
    key: 'accountName',
    label: '账户名称',
    type: 'text',
    placeholder: '请输入账户名称',
    width: '180px'
  },
  {
    key: 'transactionType',
    label: '交易类型',
    type: 'select',
    placeholder: '请选择交易类型',
    width: '150px',
    options: [
      { label: '收入', value: 'income' },
      { label: '支出', value: 'expense' },
      { label: '转账', value: 'transfer' }
    ]
  },
  {
    key: 'transactionDate',
    label: '交易日期',
    type: 'daterange',
    width: '240px'
  },
  {
    key: 'amount',
    label: '交易金额',
    type: 'amount-range'
  },
  {
    key: 'purpose',
    label: '资金用途',
    type: 'text',
    placeholder: '请输入资金用途',
    width: '200px'
  }
]

const mockData = [
  ['账户名称', '交易类型', '交易金额', '交易日期', '资金用途', '对方账户', '余额', '经办人', '凭证号', '备注'],
  ['工商银行基本户', '收入', '500000.00', '2024-01-10', '项目回款', '客户A公司', '1200000.00', '张三', 'PZ001', '合同款'],
  ['建设银行专用户', '支出', '200000.00', '2024-01-12', '材料采购', '供应商B', '800000.00', '李四', 'PZ002', ''],
  ['农业银行临时户', '转账', '100000.00', '2024-01-15', '资金调拨', '工商银行基本户', '300000.00', '王五', 'PZ003', '内部调拨'],
  ['工商银行基本户', '支出', '80000.00', '2024-01-18', '人员工资', '员工工资卡', '1120000.00', '赵六', 'PZ004', '月度工资'],
  ['建设银行专用户', '收入', '300000.00', '2024-01-20', '政府补贴', '财政局', '1100000.00', '孙七', 'PZ005', '专项补贴']
]
</script>
