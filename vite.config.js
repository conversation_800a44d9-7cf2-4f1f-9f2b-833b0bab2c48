import { fileURLToPath, URL } from 'node:url'
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueDevTools from 'vite-plugin-vue-devtools'
import { visualizer } from 'rollup-plugin-visualizer'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    vueDevTools(),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    },
  },
  build: {
    rollupOptions: {
      output: {
        manualChunks: (id) => {
          // 将node_modules中的依赖包单独打包
          if (id.includes('node_modules')) {
            // Univer.js相关包单独打包
            if (id.includes('@univerjs')) {
              return 'univer';
            }
            // 将element-plus单独打包
            if (id.includes('element-plus')) {
              return 'element-plus';
            }
            // ECharts单独打包
            if (id.includes('echarts')) {
              return 'echarts';
            }
            // Vue相关核心库
            if (id.includes('vue') || id.includes('vue-router') || id.includes('pinia')) {
              return 'vendor';
            }
            // 其他依赖
            return 'deps';
          }
        }
      }
    }
  },
  plugins: [
    vue(),
    vueDevTools(),
    // 打包分析工具（可选）
    visualizer({
      open: true,
      gzipSize: true,
      brotliSize: true,
    })
  ],
})
