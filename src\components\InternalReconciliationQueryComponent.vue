<template>
  <BaseQueryComponent
    title="内部对账"
    :query-fields="queryFields"
    :mock-data="mockData"
    @back="$emit('back')"
  />
</template>

<script setup>
import BaseQueryComponent from './BaseQueryComponent.vue'

defineEmits(['back'])

const queryFields = [
  {
    key: 'reconciliationNumber',
    label: '对账单号',
    type: 'text',
    placeholder: '请输入对账单号',
    width: '180px'
  },
  {
    key: 'department',
    label: '对账部门',
    type: 'select',
    placeholder: '请选择部门',
    width: '150px',
    options: [
      { label: '财务部', value: 'finance' },
      { label: '工程部', value: 'engineering' },
      { label: '采购部', value: 'procurement' },
      { label: '销售部', value: 'sales' },
      { label: '人事部', value: 'hr' }
    ]
  },
  {
    key: 'reconciliationDate',
    label: '对账日期',
    type: 'daterange',
    width: '240px'
  },
  {
    key: 'amount',
    label: '对账金额',
    type: 'amount-range'
  },
  {
    key: 'status',
    label: '对账状态',
    type: 'select',
    placeholder: '请选择状态',
    width: '150px',
    options: [
      { label: '待对账', value: 'pending' },
      { label: '对账中', value: 'processing' },
      { label: '已平账', value: 'balanced' },
      { label: '有差异', value: 'difference' }
    ]
  }
]

const mockData = [
  ['对账单号', '对账部门', '对账项目', '账面金额', '实际金额', '差异金额', '对账日期', '对账状态', '经办人', '备注'],
  ['DZ202401001', '工程部', '项目A材料费', '500000.00', '498000.00', '2000.00', '2024-01-10', '有差异', '张工', '运费差异'],
  ['DZ202401002', '采购部', '设备采购款', '800000.00', '800000.00', '0.00', '2024-01-15', '已平账', '李采购', ''],
  ['DZ202401003', '销售部', '客户回款', '1200000.00', '1200000.00', '0.00', '2024-01-20', '已平账', '王销售', ''],
  ['DZ202401004', '人事部', '员工借款', '50000.00', '45000.00', '5000.00', '2024-01-25', '有差异', '赵人事', '待还款确认'],
  ['DZ202401005', '财务部', '银行存款', '2000000.00', '2000000.00', '0.00', '2024-01-30', '已平账', '孙会计', '月末对账']
]
</script>
