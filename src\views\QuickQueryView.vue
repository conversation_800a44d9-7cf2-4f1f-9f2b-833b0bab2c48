<template>
  <div class="quick-query-view">
    <!-- 卡片列表视图 -->
    <div v-if="currentView === 'cards'">
      <h1>财务台账查询</h1>
      <div class="cards-container">
        <div
          v-for="(card, index) in cards"
          :key="index"
          class="card"
          @click="handleCardClick(card)"
        >
          <div class="card-icon">
            <component :is="card.icon" />
          </div>
          <div class="card-content">
            <h3>{{ card.title }}</h3>
            <p>{{ card.description }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 各种查询组件视图 -->
    <div v-else-if="currentView === 'voucher-query'">
      <VoucherQueryComponent @back="goBack" />
    </div>
    <div v-else-if="currentView === 'supplier-payable'">
      <SupplierPayableQueryComponent @back="goBack" />
    </div>
    <div v-else-if="currentView === 'contract-payable'">
      <ContractPayableQueryComponent @back="goBack" />
    </div>
    <div v-else-if="currentView === 'payment-records'">
      <PaymentRecordsQueryComponent @back="goBack" />
    </div>
    <div v-else-if="currentView === 'subcontractor-settlement'">
      <SubcontractorSettlementQueryComponent @back="goBack" />
    </div>
    <div v-else-if="currentView === 'cost-records'">
      <CostRecordsQueryComponent @back="goBack" />
    </div>
    <div v-else-if="currentView === 'fund-management'">
      <FundManagementQueryComponent @back="goBack" />
    </div>
    <div v-else-if="currentView === 'receipt-records'">
      <ReceiptRecordsQueryComponent @back="goBack" />
    </div>
    <div v-else-if="currentView === 'external-confirmation'">
      <ExternalConfirmationQueryComponent @back="goBack" />
    </div>
    <div v-else-if="currentView === 'safety-expense'">
      <SafetyExpenseQueryComponent @back="goBack" />
    </div>
    <div v-else-if="currentView === 'internal-reconciliation'">
      <InternalReconciliationQueryComponent @back="goBack" />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import VoucherQueryComponent from '@/components/VoucherQueryComponent.vue'
import SupplierPayableQueryComponent from '@/components/SupplierPayableQueryComponent.vue'
import ContractPayableQueryComponent from '@/components/ContractPayableQueryComponent.vue'
import PaymentRecordsQueryComponent from '@/components/PaymentRecordsQueryComponent.vue'
import SubcontractorSettlementQueryComponent from '@/components/SubcontractorSettlementQueryComponent.vue'
import CostRecordsQueryComponent from '@/components/CostRecordsQueryComponent.vue'
import FundManagementQueryComponent from '@/components/FundManagementQueryComponent.vue'
import ReceiptRecordsQueryComponent from '@/components/ReceiptRecordsQueryComponent.vue'
import ExternalConfirmationQueryComponent from '@/components/ExternalConfirmationQueryComponent.vue'
import SafetyExpenseQueryComponent from '@/components/SafetyExpenseQueryComponent.vue'
import InternalReconciliationQueryComponent from '@/components/InternalReconciliationQueryComponent.vue'
import {
  User,
  Document,
  Money,
  List,
  TrendCharts,
  DataAnalysis,
  CreditCard,
  Checked,
  Warning,
  Select,
  Tickets
} from '@element-plus/icons-vue'

const router = useRouter()

// 当前视图状态
const currentView = ref('cards')

// 卡片功能数据 - 使用Element Plus新版图标，所有功能都使用组件切换
const cards = [
  {
    title: '应付供应商汇总台账',
    description: '供应商应付款汇总信息',
    icon: User,
    type: 'supplier-payable'
  },
  {
    title: '应付合同汇总台账',
    description: '合同应付款汇总信息',
    icon: Document,
    type: 'contract-payable'
  },
  {
    title: '付款台账',
    description: '详细付款记录',
    icon: Money,
    type: 'payment-records'
  },
  {
    title: '分供结算台账',
    description: '分包商结算记录',
    icon: List,
    type: 'subcontractor-settlement'
  },
  {
    title: '成本台账',
    description: '项目成本明细',
    icon: TrendCharts,
    type: 'cost-records'
  },
  {
    title: '资金整理',
    description: '资金流水整理',
    icon: DataAnalysis,
    type: 'fund-management'
  },
  {
    title: '收款台账',
    description: '详细收款记录',
    icon: CreditCard,
    type: 'receipt-records'
  },
  {
    title: '外部确权台账',
    description: '外部确权记录',
    icon: Select,
    type: 'external-confirmation'
  },
  {
    title: '安全费台账',
    description: '安全费用支出记录',
    icon: Warning,
    type: 'safety-expense'
  },
  {
    title: '内部对账',
    description: '内部对账记录',
    icon: Checked,
    type: 'internal-reconciliation'
  },
  {
    title: '凭证查询',
    description: '财务凭证查询和检索',
    icon: Tickets,
    type: 'voucher-query'
  }
]

// 处理卡片点击
function handleCardClick(card) {
  // 所有卡片都切换到对应的查询组件，不再跳转路由
  currentView.value = card.type
}

// 返回卡片列表
function goBack() {
  currentView.value = 'cards'
}
</script>

<style>
body {
  background: #f8f9fb;
}

.quick-query-view {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
}

h1 {
  font-size: 2.2rem;
  font-weight: 600;
  margin-bottom: 32px;
  color: #222;
  letter-spacing: 2px;
}

.cards-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 24px;
  width: 100%;
  max-width: 1200px;
}

.card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  padding: 24px;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 24px rgba(64, 158, 255, 0.15);
}

.card-icon {
  width: 60px;
  height: 60px;
  background: #ecf5ff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  color: #409eff;
}

.card-icon i {
  font-size: 28px;
}

.card-content {
  flex: 1;
}

.card-content h3 {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #333;
}

.card-content p {
  font-size: 14px;
  color: #666;
  margin: 0;
}
</style>