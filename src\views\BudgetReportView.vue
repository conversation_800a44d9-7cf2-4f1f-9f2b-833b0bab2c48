<template>
  <div class="view-container">
    <div class="section-header">
      <h2>财务报表结账预计系统</h2>
      <p>结账前数据校验器</p>
    </div>
    
    <div class="action-buttons">
      <button @click="fetchData" class="action-button fetch">取数</button>
      <button @click="fetchDataDifference" class="action-button fetch">获取缺失差异</button>
      <button @click="intelfillCloseTamplate" class="action-button fetch">填充结账模板</button>
      <button @click="saveData" class="action-button save">推送结账后台</button>
      <button @click="saveData2" class="action-button save">保存</button>
      <button @click="openRemarkDialog" class="action-button export">保存至新版本</button>
      
      <div class="period-input-group">
        <div class="period-input-container">
          <input 
            v-model="selectedPeriod"
            type="text" 
            class="period-input" 
            readonly 
            placeholder="请选择版本..."
          />
          <button @click.stop="toggleDropdown" class="action-button export">导入</button>
        </div>
        <transition name="fade">
          <div v-if="showDropdown" class="dropdown-content" @click.stop>
            <div v-if="loadingPeriods" class="loading-text">加载中...</div>
            <div v-else-if="periods.length === 0" class="no-data">没有可用的数据</div>
            <template v-else>
              <button 
                v-for="(period, idx) in periods" 
                :key="idx" 
                class="dropdown-item"
                style="display: flex; align-items: center; justify-content: space-between; gap: 8px;"
              >
                <span @click="selectPeriod(period[0], `${period[0]} - ${period[1]} - ${period[2]}`)" style="flex:1; text-align:left; cursor:pointer;">
                  {{ period[0] }} - {{ period[1] }} - {{ period[2] }}
                </span>
                <button 
                  @click.stop="deletePeriod(period[0])" 
                  style="color: #f5222d; background: none; border: none; cursor: pointer; font-size: 13px; padding: 0 6px;"
                  title="删除此版本"
                >删除</button>
              </button>
            </template>
          </div>
        </transition>
      </div>
      <button @click="copySheet" class="action-button export">复制表格</button>
      <button @click="triggerFileInput" class="action-button export">导入本地excel表格</button>
      <input ref="fileInput" type="file" accept=".xlsx,.xls" style="display:none" @change="importExcel" />
    </div>
    <div ref="container" class="table-container"></div>
    <!-- 备注输入弹窗 -->
    <div v-if="showRemarkDialog" class="remark-dialog-mask">
      <div class="remark-dialog">
        <div class="remark-title">请输入备注</div>
        <textarea v-model="remarkText" rows="3" class="remark-input" placeholder="请输入保存备注"></textarea>
        <div class="remark-actions">
          <button @click="confirmRemark" class="action-button save">确定</button>
          <button @click="showRemarkDialog = false" class="action-button export">取消</button>
        </div>
      </div>
    </div>
  </div>
</template>
 
<script lang="ts" setup>
import { onMounted, onBeforeUnmount, ref } from 'vue'
 
import { createUniver, defaultTheme, FUniver, LocaleType, merge, Univer } from '@univerjs/presets';
import { UniverSheetsCorePreset } from '@univerjs/presets/preset-sheets-core';
import UniverPresetSheetsCoreZhCN from '@univerjs/presets/preset-sheets-core/locales/zh-CN';
import '@univerjs/presets/lib/styles/preset-sheets-core.css';


import { UniverSheetsConditionalFormattingPreset } from '@univerjs/presets/preset-sheets-conditional-formatting'
import sheetsConditionalFormattingZhCN from '@univerjs/presets/preset-sheets-conditional-formatting/locales/zh-CN'
import '@univerjs/presets/lib/styles/preset-sheets-conditional-formatting.css'

import { UniverSheetsDataValidationPreset } from '@univerjs/presets/preset-sheets-data-validation'
import sheetsDataValidationZhCN from '@univerjs/presets/preset-sheets-data-validation/locales/zh-CN'
import '@univerjs/presets/lib/styles/preset-sheets-data-validation.css'

import { UniverSheetsDrawingPreset } from '@univerjs/presets/preset-sheets-drawing'
import sheetsDrawingZhCN from '@univerjs/presets/preset-sheets-drawing/locales/zh-CN'
import '@univerjs/presets/lib/styles/preset-sheets-drawing.css'

import { UniverSheetsFilterPreset } from '@univerjs/presets/preset-sheets-filter'
import sheetsFilterZhCN from '@univerjs/presets/preset-sheets-filter/locales/zh-CN'
import '@univerjs/presets/lib/styles/preset-sheets-filter.css'

import { UniverSheetsHyperLinkPreset } from '@univerjs/presets/preset-sheets-hyper-link'
import sheetsHyperLinkZhCN from '@univerjs/presets/preset-sheets-hyper-link/locales/zh-CN'
import '@univerjs/presets/lib/styles/preset-sheets-hyper-link.css'

import fillDifference  from '../scripts/fillDifference'
import fetchSnapShot from '../scripts/snapShot'
import fillCloseTamplate from '../scripts/fillCloseTamplate'
import ExcelJS from 'exceljs';

const container = ref<HTMLElement | null>(null)
const fileInput = ref<HTMLInputElement | null>(null)
let univerInstance: Univer | null = null
let univerAPIInstance: FUniver | null = null

const showDropdown = ref(false);
const periods = ref<Array<{id: string, name: string}>>([]);
const loadingPeriods = ref(false);
const selectedPeriod = ref('');
const selectedPeriodId = ref('');

const toggleDropdown = async () => {
  console.log('Toggle dropdown called, current state:', showDropdown.value);
  showDropdown.value = !showDropdown.value;
  if (showDropdown.value && periods.value.length === 0) {
    console.log('Fetching available periods...');
    await fetchAvailablePeriods();
  } else {
    console.log('Using cached periods:', periods.value);
  }
};

const fetchAvailablePeriods = async () => {
  try {
    loadingPeriods.value = true;
    console.log('Starting to fetch periods from API...');
    const response = await fetch('http://localhost:8000/api/get-available-periods', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({})
    });
    
    console.log('API response status:', response.status);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error('Error response:', errorText);
      throw new Error(`获取期数列表失败: ${response.status} ${response.statusText}`);
    }
    
    const data = await response.json();
    console.log('Received periods data:', data);
    periods.value = data;
  } catch (error) {
    console.error('获取期数列表时出错:', error);
    // 显示错误提示
    alert('获取期数列表失败: ' + (error.message || '未知错误'));
  } finally {
    loadingPeriods.value = false;
    console.log('Finished loading periods, loading state:', loadingPeriods.value);
  }
};

const selectPeriod = async (periodId: string, periodLabel: string) => {
  try {
    selectedPeriod.value = periodLabel;
    selectedPeriodId.value = periodId;
    showDropdown.value = false;
    const response = await fetch('http://localhost:8000/api/get-snapsheet', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ periodId })
    });
    
    if (!response.ok) {
      throw new Error(`获取快照失败: ${response.status} ${response.statusText}`);
    }

    const snapsheet = await response.json();
    const unitId = univerAPIInstance?.getActiveWorkbook()?.getId();
    if(unitId) {
      univerAPIInstance?.disposeUnit(unitId);
    }
    univerAPIInstance?.createWorkbook(snapsheet);
  } catch (error) {
    console.error('导入数据时出错:', error);
    alert('导入数据失败: ' + (error.message || '未知错误'));
  }
};

const handleClickOutside = (event: MouseEvent) => {
  const target = event.target as HTMLElement;
  if (!target.closest('.dropdown')) {
    showDropdown.value = false;
  }
};

// Function to automatically select the latest period
const selectLatestPeriod = async () => {
  try {
    await fetchAvailablePeriods();
    if (periods.value.length > 0) {
      // Assuming periods are sorted, get the last one (latest)
      const latestPeriod = periods.value[periods.value.length - 1];
      const periodLabel = `${latestPeriod[0]} - ${latestPeriod[1]} - ${latestPeriod[2]}`;
      await selectPeriod(latestPeriod[0], periodLabel);
    }
  } catch (error) {
    console.error('Failed to select latest period:', error);
  }
};

onMounted(async () => {
  // First initialize the Univer instance
  const { univer, univerAPI } = createUniver({
    locale: LocaleType.ZH_CN,
    locales: {
      [LocaleType.ZH_CN]: merge(
        {}, 
        UniverPresetSheetsCoreZhCN,
        sheetsConditionalFormattingZhCN,
        sheetsDataValidationZhCN,
        sheetsDrawingZhCN,
        sheetsFilterZhCN,
        sheetsHyperLinkZhCN,
      ),
    },
    theme: defaultTheme,
    presets: [
      UniverSheetsCorePreset({
        container: container.value as HTMLElement,
      }),
      UniverSheetsConditionalFormattingPreset(),
      UniverSheetsDataValidationPreset(),
      UniverSheetsDrawingPreset(),
      UniverSheetsFilterPreset(),
      UniverSheetsHyperLinkPreset(),
    ],
  })
  //const fWorkbook1 = univerAPI.createWorkbook({id:"Sheet1",name:"财务报表"})
  //const sheet1=fWorkbook1.create("收入成本测算",100,10)
  //const sheet2=fWorkbook1.create("快速取数",100,10)
  //fWorkbook1.deleteSheet(fWorkbook1.getSheetByName("Sheet1"))
  //sheet1.activate()
  univerInstance = univer;
  univerAPIInstance = univerAPI;
  fetchSnapShot(univerAPI);
  document.addEventListener('click', handleClickOutside);
  
  // After initialization, try to select the latest period
  await selectLatestPeriod();
  // Automatically fetch and select the latest period when component mounts
  selectLatestPeriod();
})

onBeforeUnmount(() => {
  univerInstance?.dispose()
  univerAPIInstance?.dispose()
  univerInstance = null
  univerAPIInstance = null
  document.removeEventListener('click', handleClickOutside);
})

async function fetchData() {
  try {
    const response = await fetch('http://localhost:8000/api/budget-data', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    });
    const data = await response.json();
    
    if (!Array.isArray(data)) {
      throw new Error('返回数据格式错误');
    }
    const sheet0=univerAPIInstance?.getActiveWorkbook()?.getSheetByName('收入成本测算');
    const arr=sheet0.getRange(0,0,sheet0.getLastRow()+1,sheet0.getLastColumn()+1).getValues();
    var localIndex=0;
    for(let i=0;i<arr[0].length;i++){
            if(arr[0][i]=='定位符'){localIndex=i}
        };
    var localMap=new Map();
    for(let i=1;i<arr.length;i++){
            localMap.set(arr[i][localIndex],i)
        };
    
    const sheet = univerAPIInstance?.getActiveWorkbook()?.getSheetByName('快速取数');
    if (sheet) {
      sheet.deleteRows(1, sheet.getLastRow())
      sheet.setRowCount(data[0].length)
      sheet.setColumnCount(data[0][0].length)
      sheet.getRange(0, 0, data[0].length, data[0][0].length).setValues(data[0])
    }

    for(let i=1;i<data[0].length;i++){
      if (!localMap.has(data[0][i][0])) {
        sheet.getRange(i, 0).setFontColor('red')
      }
    }
    
    const sheet2 = univerAPIInstance?.getActiveWorkbook()?.getSheetByName('快速科目余额表');
    if (sheet2) {
      sheet2.deleteRows(1, sheet2.getLastRow())
      sheet2.setRowCount(data[1].length)
      sheet2.setColumnCount(data[1][0].length)
      sheet2.getRange(0, 0, data[1].length, data[1][0].length).setValues(data[1])
    }
  } catch (error) {
    alert('获取数据失败: ' + error.message);
  }
}


const saveData = async () => {
  try {
    const sheet = univerAPIInstance?.getActiveWorkbook()?.getSheetByName('收入成本测算');
    if (!sheet) {
      throw new Error('无法获取收入成本测算表');
    }
    const range = sheet.getRange(0, 0, sheet.getLastRow()+1, sheet.getLastColumn()+1);
    const array1= range.getValues();

    const sheet2 = univerAPIInstance?.getActiveWorkbook()?.getSheetByName('独立结账模板收入成本');
    if (!sheet2) {
      throw new Error('独立结账模板收入成本');
    }
    const range2 = sheet2.getRange(0, 0, sheet2.getLastRow()+1, sheet2.getLastColumn()+1);
    const array2 = range2.getValues();

    const sheet3 = univerAPIInstance?.getActiveWorkbook()?.getSheetByName('独立结账模板安全费');
    if (!sheet3) {
      throw new Error('独立结账模板安全费');
    }
    const range3 = sheet3.getRange(0, 0, sheet3.getLastRow()+1, sheet3.getLastColumn()+1);
    const array3 =range3.getValues();

    const sheet4 = univerAPIInstance?.getActiveWorkbook()?.getSheetByName('批量暂估');
    if (!sheet4) {
      throw new Error('批量暂估');
    }
    const range4 = sheet4.getRange(0, 0, sheet4.getLastRow()+1, sheet4.getLastColumn()+1);
    const array4 =range4.getValues();

    const data = {"收入成本测算":array1,"独立结账模板收入成本":array2,"独立结账模板安全费":array3,"批量暂估":array4}
    const response = await fetch('http://localhost:8000/api/save-budget', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data),
      // 确保请求不会被缓存
      cache: 'no-cache'
    });
    
    if (!response.ok) {
      throw new Error(`保存失败: ${response.status} ${response.statusText}`);
    }

    alert('数据已成功保存！');
  } catch (error) {
    console.error('保存数据出错:', error);
    alert('保存数据失败: ' + error.message);
  }
}

const showRemarkDialog = ref(false)
const remarkText = ref('')

function openRemarkDialog() {
  remarkText.value = ''
  showRemarkDialog.value = true
}

async function intelfillCloseTamplate() {
  await fillCloseTamplate(univerAPIInstance)
}

async function confirmRemark() {
  showRemarkDialog.value = false
  await exportData(remarkText.value)
}

const exportData = async (remark = '') => {
  const snapsheet = univerAPIInstance?.getActiveWorkbook()?.save();
  try {
    console.log('开始保存快照...');
    const response = await fetch('http://localhost:8000/api/save-snapsheet', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify([
        snapsheet,
        remark] // 备注字段
      ),
      cache: 'no-cache'
    });
    
    console.log('请求发送完成，状态:', response.status, response.ok ? '成功' : '失败');

    if (!response.ok) {
      throw new Error(`保存快照失败: ${response.status} ${response.statusText}`);
    }

    alert('快照已成功保存！');
  } catch (error) {
    console.error('保存快照出错:', error);
    alert('保存快照失败: ' + error.message);
  }
}

const saveData2 = async () => {
  const snapsheet = univerAPIInstance?.getActiveWorkbook()?.save();
  try {
    console.log('开始保存快照...');
    const response = await fetch('http://localhost:8000/api/save-snapsheet-new', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify([
        selectedPeriodId.value,
        snapsheet
      ]),
      cache: 'no-cache'
    });
    
    console.log('请求发送完成，状态:', response.status, response.ok ? '成功' : '失败');

    if (!response.ok) {
      throw new Error(`保存快照失败: ${response.status} ${response.statusText}`);
    }

    alert('快照已成功保存！');
  } catch (error) {
    console.error('保存快照出错:', error);
    alert('保存快照失败: ' + error.message);
  }
}

const importData = async () => {
  // 这个函数已经被上面的selectPeriod函数替代
}

const copySheet =() => {
  const snapsheet = univerAPIInstance?.getActiveWorkbook()?.getSheetByName("收入成本测算表").getSheet().getSnapshot();
  univerAPIInstance?.getActiveWorkbook()?.create
  //去除sanpsheet里面的id和name
  const copiedSnapsheet = JSON.parse(JSON.stringify(snapsheet));
  delete copiedSnapsheet.id;
  delete copiedSnapsheet.name;
  univerAPIInstance?.getActiveWorkbook()?.create("新表",copiedSnapsheet.rowCount,copiedSnapsheet.columnCount,{index:2,sheet:copiedSnapsheet});

}

// 触发隐藏文件选择框
function triggerFileInput() {
  fileInput.value?.click();
}

// 导入excel并写入universheet
async function importExcel(event: Event) {
  const input = event.target as HTMLInputElement;
  if (!input.files || input.files.length === 0) return;
  const file = input.files[0];
  const reader = new FileReader();
  reader.onload = async (e) => {
    const buffer = e.target?.result;
    if (!buffer) return;
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.load(buffer as ArrayBuffer);
    const univerWorkbook = univerAPIInstance?.getActiveWorkbook();
    if (!univerWorkbook) return;
    for (const worksheet of workbook.worksheets) {
      // 检查是否存在同名sheet，不存在则创建
      let univerSheet = univerWorkbook.getSheetByName(worksheet.name);
      if (!univerSheet) {
        univerSheet = univerWorkbook.create(worksheet.name, worksheet.rowCount, worksheet.columnCount);
      }
      // 读取exceljs sheet数据，支持公式
      const rows = [];
      worksheet.eachRow({ includeEmpty: true }, (row) => {
        const rowData = [];
        // 获取行中所有单元格（包括空值）
        for (let col = 1; col <= worksheet.columnCount; col++) {
          const cell = row.getCell(col);
          if (cell.formula) {
            // 处理公式，移除_xlfn.前缀并添加等号
            const formula = cell.formula.replace(/_xlfn\./g, '');
            rowData.push('=' + formula);
          } else {
            // 否则使用原始值，如果为undefined则设为null
            rowData.push(cell.value !== undefined ? cell.value : null);
          }
        }
        rows.push(rowData);
      });
      // 写入universheet
      if (univerSheet) {
        const range = univerSheet.getRange(0, 0, rows.length, worksheet.columnCount);
        console.log(rows)
        range.setValues(rows);
      }
    }
    input.value = '';
  };
  reader.readAsArrayBuffer(file);
}

const deletePeriod = async (periodId: string) => {
  if (!window.confirm('确定要删除该版本吗？此操作不可恢复！')) return;
  try {
    const response = await fetch('http://localhost:8000/api/delete-snapsheet', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(periodId)
    });
    if (!response.ok) {
      throw new Error(`删除失败: ${response.status} ${response.statusText}`);
    }
    alert('删除成功！');
    await fetchAvailablePeriods(); // 刷新列表
  } catch (error: any) {
    alert('删除失败: ' + (error.message || '未知错误'));
  }
}

</script>

<style scoped>
.view-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.action-buttons {
  display: flex;
  gap: 5px;
  margin-bottom: 5px;
}

.action-button {
  padding: 4px 7px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s;
}

.action-button.fetch {
  background-color: #1890ff;
  color: white;
}

.action-button.save {
  background-color: #52c41a;
  color: white;
}

.action-button.tax {
  background-color: #722ed1;
  color: white;
}

.action-button.declaration {
  background-color: #fa8c16;
  color: white;
}

.action-button.add {
  background-color: #1890ff;
  color: white;
}

.action-button.export {
  background-color: #fa8c16;
  color: white;
}

.table-container {
  flex: 1;
  width: 100%;
}

.dropdown {
  position: relative;
  display: inline-block;
  z-index: 1000;
}

.dropdown-content {
  position: absolute;
  top: calc(100% + 5px);
  left: 0;
  background-color: #fff;
  min-width: 200px;
  max-height: 400px;
  overflow-y: auto;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 1001;
  border-radius: 4px;
  border: 1px solid #d9d9d9;
  padding: 8px 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

.dropdown-item {
  display: block;
  padding: 8px 16px;
  color: #333;
  font-size: 14px;
  line-height: 1.5;
  cursor: pointer;
  transition: all 0.3s;
  white-space: nowrap;
  text-align: left;
  background: transparent;
  border: none;
  width: 100%;
  margin: 0;
}

.dropdown-item:hover {
  background-color: #f0f7ff;
  color: #1890ff;
}

/* Loading and no data states */
.dropdown-content > div {
  padding: 12px 16px;
  color: #666;
  font-size: 14px;
  text-align: center;
}

/* Fade transition for smoother appearance */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.remark-dialog-mask {
  position: fixed;
  left: 0; top: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.2);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
}
.remark-dialog {
  background: #fff;
  border-radius: 8px;
  padding: 24px 20px 16px 20px;
  min-width: 320px;
  box-shadow: 0 2px 16px rgba(0,0,0,0.18);
  display: flex;
  flex-direction: column;
  gap: 12px;
}
.remark-title {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 8px;
}
.remark-input {
  width: 100%;
  min-height: 60px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 6px;
  font-size: 14px;
  resize: vertical;
}
.remark-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  margin-top: 8px;
}

/* 在已有的样式后添加 */
.period-input-group {
  position: relative;
  display: inline-block;
}

.period-input-container {
  display: flex;
  gap: 5px;
  align-items: center;
}

.period-input {
  width: 250px;
  height: 32px;
  padding: 4px 11px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
  background-color: #fff;
  cursor: default;
}

.period-input:hover {
  border-color: #40a9ff;
}

.dropdown-content {
  position: absolute;
  top: calc(100% + 5px);
  left: 0;
  width: 250px; /* 与输入框宽度对齐 */
}
</style>