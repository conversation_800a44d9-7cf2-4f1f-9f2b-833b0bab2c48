<template>
  <BaseQueryComponent
    title="外部确权台账"
    :query-fields="queryFields"
    :mock-data="mockData"
    @back="$emit('back')"
  />
</template>

<script setup>
import BaseQueryComponent from './BaseQueryComponent.vue'

defineEmits(['back'])

const queryFields = [
  {
    key: 'confirmationNumber',
    label: '确权单号',
    type: 'text',
    placeholder: '请输入确权单号',
    width: '180px'
  },
  {
    key: 'externalParty',
    label: '外部单位',
    type: 'text',
    placeholder: '请输入外部单位名称',
    width: '200px'
  },
  {
    key: 'confirmationDate',
    label: '确权日期',
    type: 'daterange',
    width: '240px'
  },
  {
    key: 'amount',
    label: '确权金额',
    type: 'amount-range'
  },
  {
    key: 'status',
    label: '确权状态',
    type: 'select',
    placeholder: '请选择状态',
    width: '150px',
    options: [
      { label: '待确权', value: 'pending' },
      { label: '已确权', value: 'confirmed' },
      { label: '有争议', value: 'disputed' },
      { label: '已撤销', value: 'cancelled' }
    ]
  }
]

const mockData = [
  ['确权单号', '外部单位', '确权金额', '确权日期', '确权类型', '确权状态', '争议金额', '负责人', '联系方式', '备注'],
  ['QQ202401001', '建设银行', '500000.00', '2024-01-10', '应收账款', '已确权', '0.00', '张经理', '13800138001', ''],
  ['QQ202401002', '供应商A公司', '200000.00', '2024-01-15', '应付账款', '有争议', '50000.00', '李主管', '13800138002', '质量问题'],
  ['QQ202401003', '客户B公司', '800000.00', '2024-01-20', '应收账款', '已确权', '0.00', '王总监', '13800138003', ''],
  ['QQ202401004', '分包商C', '300000.00', '2024-01-25', '应付账款', '待确权', '0.00', '赵工', '13800138004', '工程量核实中'],
  ['QQ202401005', '租赁公司D', '120000.00', '2024-01-30', '预付账款', '已撤销', '0.00', '孙会计', '13800138005', '合同终止']
]
</script>
