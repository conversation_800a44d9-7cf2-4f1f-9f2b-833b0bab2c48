<template>
  <BaseQueryComponent
    title="分供结算台账"
    :query-fields="queryFields"
    :mock-data="mockData"
    @back="$emit('back')"
  />
</template>

<script setup>
import BaseQueryComponent from './BaseQueryComponent.vue'

defineEmits(['back'])

const queryFields = [
  {
    key: 'subcontractorName',
    label: '分包商名称',
    type: 'text',
    placeholder: '请输入分包商名称',
    width: '200px'
  },
  {
    key: 'projectName',
    label: '项目名称',
    type: 'text',
    placeholder: '请输入项目名称',
    width: '200px'
  },
  {
    key: 'settlementDate',
    label: '结算日期',
    type: 'daterange',
    width: '240px'
  },
  {
    key: 'amount',
    label: '结算金额',
    type: 'amount-range'
  },
  {
    key: 'status',
    label: '结算状态',
    type: 'select',
    placeholder: '请选择状态',
    width: '150px',
    options: [
      { label: '待结算', value: 'pending' },
      { label: '已结算', value: 'settled' },
      { label: '部分结算', value: 'partial' }
    ]
  }
]

const mockData = [
  ['分包商名称', '项目名称', '合同金额', '已结算金额', '待结算金额', '结算日期', '结算状态', '工程进度', '质量评级', '负责人'],
  ['北京建筑队', '办公楼A栋', '500000.00', '400000.00', '100000.00', '2024-01-15', '部分结算', '80%', 'A级', '张队长'],
  ['上海装修公司', '厂房装修', '300000.00', '300000.00', '0.00', '2024-01-20', '已结算', '100%', 'A级', '李经理'],
  ['广州水电安装', '水电改造', '150000.00', '100000.00', '50000.00', '2024-01-25', '部分结算', '70%', 'B级', '王师傅'],
  ['深圳钢结构', '钢结构工程', '800000.00', '0.00', '800000.00', '2024-02-01', '待结算', '60%', 'A级', '赵工'],
  ['天津防水队', '屋面防水', '120000.00', '120000.00', '0.00', '2024-02-05', '已结算', '100%', 'A级', '孙师傅']
]
</script>
